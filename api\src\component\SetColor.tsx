import "react";
import { useState } from "react";


const SetColor = () => {
  const [color, setColor] = useState("?");

  return ( 
    <>
       <h1>React Hooks example:</h1>
    <div className="clear-both">
      <button
        className="bg-blue-500 hover:bg-gray-500 text-white mr-2.5 my-3 rounded-md px-3 py-2"
        type="button"
        onClick={() => setColor("Blue")}
      ><span className="text-blue"></span>Blue</button>
      <button
        className="bg-red-500 hover:bg-gray-500 text-white mr-2.5 my-3 rounded-md px-3 py-2"
        type="button"
        onClick={() => setColor("Red")}
      >Red</button>
      <button
        className="bg-pink-500 hover:bg-gray-500 text-white mr-2.5 my-3 rounded-md px-3 py-2"
        type="button"
        onClick={() => setColor("Pink")}
      >Pink</button>
      <button
        className="bg-green-500 hover:bg-gray-500 text-white my-3 rounded-md px-3 py-2"
        type="button"
        onClick={() => setColor("Green")}
      >Green</button>
      </div>
      <h1>My favorite color is <span className="text-2xl font-bold">{color}!</span></h1>
    </>
  );
}


export default SetColor