import "react";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>er as Router, <PERSON>} from 'react-router-dom';
import { Outlet } from "react-router-dom";





const Header  = () => {
  return (
    <> 
    <Router> 
      <div className="bg-gray-200 p-6 rounded-t-md mb-7">
      <nav>
        <ul>
          <li className="inline-flex mr-4">
            <Link to="/">Home</Link>
          </li>
          <li className="inline-flex mr-4">
          <Link to="/Hooks">React Hooks</Link> 
          </li>
          <li className="inline-flex mr-4">
          <Link to="/ApiOne">Button example</Link>
          </li>
        </ul>
      </nav>
      </div>
      <Outlet />
    </Router>
 
    
    </>
  );
}


export default Header












