import 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { addTodo, fetchTodos } from '../api-mock-data';
import TodoCard from '../component/TodoCard';
import { useState } from 'react';

export default function Demo() {
    const queryClient = useQueryClient();
    const [search] = useState("");
    const [title, setTitle] = useState("");
    
    const {data: todos, isLoading} = useQuery({
        queryKey: ["todos", { search }],
        queryFn: () => fetchTodos(search),
        staleTime: Infinity,
        //cacheTime: 0,
    });

    const { mutateAsync: addTodoMutation } = useMutation({
        mutationFn: addTodo,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["todos"] })
        },
    });

    if (isLoading){
        return <div>Loading...</div>
    }


    return (
        <div> 
        <div>
            <input type="text" 
                   onChange={(e) => setTitle(e.target.value)} 
                   value={title}
                   />
                   <button onClick={async () => {
                    try {
                        await addTodoMutation({ title });
                        setTitle("");
                    } catch (e) {
                        console.log(e);
                    }
                   }}
                >
                    Add Todo
                </button>
            </div> 
           {todos?.map((todo) => {
            return <TodoCard key={todo.id} todo={todo} />;
           })}
       
        </div>
    );
}