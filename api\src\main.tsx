import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter, Route, Routes} from "react-router-dom";
import App from './App.tsx';
import Header from './component/header.tsx';
import Footer from './component/footer.tsx';
import ApiOne from './pages/ApiOne.tsx';
import Hooks from './pages/Hooks.tsx';
import NoPage from './pages/NoPage.tsx';
import './index.css';
import './App.css';



createRoot(document.getElementById('root')!).render(
  
 <StrictMode>
  <Header />
    <BrowserRouter>
      <Routes>
           <Route index element={<App />} />
           <Route path="/home" element={<App />} />
           <Route path="/Hooks" element={<Hooks />} />
           <Route path="/ApiOne" element={<ApiOne />} />
           <Route path="*" element={<NoPage />} />
      </Routes>
   </BrowserRouter>
<Footer />
</StrictMode>,
 
)

