{"name": "api", "private": true, "version": "0.0.0", "type": "module", "homepage": ".", "scripts": {"start": "webpack serve - mode development - open", "build": "vite build", "dev": "vite", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@apollo/client": "^3.11.8", "@reduxjs/toolkit": "^2.2.7", "@tanstack/react-query": "^5.59.19", "graphql": "^16.9.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-query": "^3.39.3", "react-router": "^6.27.0", "react-router-dom": "^6.26.2"}, "devDependencies": {"@babel/preset-react": "^7.24.7", "@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "react-snapshot": "^1.3.0", "tailwindcss": "^3.4.12", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.3", "webpack": "^5.94.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0"}, "overrides": {"vite": {"rollup": "npm:@rollup/wasm-node"}}, "optionalDependencies": {"@rollup/rollup-win32-x64-msvc": "4.20.0"}}