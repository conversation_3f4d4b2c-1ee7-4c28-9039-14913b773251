import React, { Component} from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Demo from './pages/demo.tsx';

const data = ['apple', 'banana', 'orange'];

const fruitsList = data.map((fruit, index) => {
  return <li key={index}>{fruit}</li>;
});

const queryClient = new QueryClient();

class App extends Component {
  state = {
    isLoaded: false,
    items: [],
    address: []
  }

 // componentDidMount() {
 //   fetch('https://jsonplaceholder.typicode.com/users')
 //   .then(res => res.json())
 //   .then(json => {
 //     this.setState({
  //      isLoaded: true,
 //      items: json,
  //    })
  //  });
 // };

  componentDidMount() {
      fetch('https://jsonplaceholder.typicode.com/users')
    .then(res => res.json())
    .then(json => {
      this.setState({
        isLoaded: true,
        items: json,
      })
    });
  };


render() {

  var { isLoaded, items } = this.state;
  if (!isLoaded) {
    return <div>loading...</div>;
  }
 
  else {

  return (
    <div>
   <QueryClientProvider client={queryClient}>
    <Demo />
  </QueryClientProvider>

  <br></br>

  
      <h1 className="text-md font-bold pb-5">
      API fetch example
    </h1>

    <div className=''>
      <ul className='clear-both text-black'>
        {items.map((item: { id:React.Key, 
                            name:string, 
                            email:string, 
                            username:string, 
                            website:string, 
                            address: {
                              suite:string, 
                              street:string, 
                              city:string, 
                              zipcode:string, 
                              geo: {
                                lat:string, 
                                lng:string
                              }
                               } }) => (
          <li style={{height:350}} className='li-30-b name-email box-shadow-round p-7 mr-5' key={item.id}>
           <strong>Name:</strong> {item.name} 
           <div className='spacer0'></div>
           <strong>Email:</strong> {item.email}
           <div className='spacer0'></div>
           <strong>Username:</strong> {item.username}
           <div className='spacer0'></div>
           <strong>Website:</strong> {item.website}
           <div className='spacer15'></div>
           <strong>Address:</strong> <br></br> 
            {item.address.suite} {item.address.street}, <br></br> 
            {item.address.city}, <br></br> 
            {item.address.zipcode}, <br></br> <br></br> 
            <strong>Latitude:</strong> {item.address.geo.lat} <br></br> 
            <strong>Longitude:</strong> {item.address.geo.lng} <br></br> 
          </li>
        ))}
      </ul>
      <div className='spacer0'></div>

<ul>
  {fruitsList}
</ul>


      <div className='spacer0'></div>
</div>
    
    </div>
  );
}
}
 
}


export default App


