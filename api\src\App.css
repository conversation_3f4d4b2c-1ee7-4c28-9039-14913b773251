#root {
  max-width: 1280px;
  margin: auto;
}
input[type="text"] {
  border-width: 2px;
  border-style: solid;
  border-color: #ccc;
  border-radius: 5px;
  padding: 7px 10px;
  margin-right: 10px;
  margin-bottom: 20px;
}
input[type="checkbox"] {
  margin:0 10px 0 0;
  cursor: pointer;
}
label{
  cursor: pointer;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 15px;
  /*display: flex;*/
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 14px;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}
.li-30-b {
  margin-bottom: 30px;
}
.heading-item{
  background-color: #535bf2;
  color: #ffffff;
  padding: 5px 10px;
  border-radius: 2px;
}
.heading-item2{
  background-color: #058f43;
  color: #ffffff;
  padding: 5px 10px;
  border-radius: 2px;
}
.spacer15{
  height: 15px;
  clear: both;
}
.spacer5{
  height: 5px;
  clear: both;
}
.spacer0{
  height: 0px;
  clear: both;
}
li{
  list-style: none;
}
.floatL{
  float: left;
}
.user-web, .name-email{
  width:300px;
  float: left;
}
.clear-both{
  display:block;
  clear: both;
}
.box-shadow, .box-shadow-round {
  box-shadow: 0 6px 25px 0 rgba(0, 0, 0, .12);
  border-radius: 5px;
  border: 1px solid #e1e1e1;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
} 

